#!/usr/bin/env python3
"""
Тест потока работы менеджера в разделе "Тесты"
"""

import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_manager_flow():
    """Тест потока работы менеджера"""
    try:
        print("🧪 Тестируем поток работы менеджера в разделе 'Тесты'...")
        
        # Проверяем импорт состояний
        from manager.states.states_tests import ManagerTestsStatisticsStates
        print("✅ Состояния менеджера импортированы")
        
        # Проверяем, что у менеджера есть дополнительные состояния для выбора персонала
        staff_selection_states = [
            ManagerTestsStatisticsStates.select_staff_type_for_course_entry,
            ManagerTestsStatisticsStates.select_staff_for_course_entry,
            ManagerTestsStatisticsStates.select_staff_type_for_month_entry,
            ManagerTestsStatisticsStates.select_staff_for_month_entry,
            ManagerTestsStatisticsStates.select_staff_type_for_month_control,
            ManagerTestsStatisticsStates.select_staff_for_month_control,
            ManagerTestsStatisticsStates.select_staff_type_for_ent,
            ManagerTestsStatisticsStates.select_staff_for_ent,
        ]
        print(f"✅ Определено {len(staff_selection_states)} состояний выбора персонала")
        
        # Проверяем импорт обработчиков
        from manager.handlers.tests import router
        print("✅ Обработчики менеджера импортированы")
        
        # Проверяем клавиатуры
        from manager.keyboards.analytics import get_staff_type_selection_kb, get_staff_kb
        
        # Тестируем создание клавиатуры выбора типа персонала
        staff_type_kb = get_staff_type_selection_kb()
        print("✅ Клавиатура выбора типа персонала создана")
        
        # Проверяем, что в клавиатуре есть кнопки для кураторов и преподавателей
        has_curator_button = False
        has_teacher_button = False
        
        for row in staff_type_kb.inline_keyboard:
            for button in row:
                if "Кураторы" in button.text and button.callback_data == "staff_type_curator":
                    has_curator_button = True
                elif "Преподаватели" in button.text and button.callback_data == "staff_type_teacher":
                    has_teacher_button = True
        
        if has_curator_button and has_teacher_button:
            print("✅ Клавиатура содержит кнопки для кураторов и преподавателей")
        else:
            print("❌ Клавиатура не содержит нужные кнопки")
            return False
        
        # Тестируем создание клавиатуры выбора кураторов
        try:
            curator_kb = await get_staff_kb("curator")
            print("✅ Клавиатура выбора кураторов создана")
        except Exception as e:
            print(f"⚠️ Клавиатура кураторов: {e} (нормально, если нет подключения к БД)")
        
        # Тестируем создание клавиатуры выбора преподавателей
        try:
            teacher_kb = await get_staff_kb("teacher")
            print("✅ Клавиатура выбора преподавателей создана")
        except Exception as e:
            print(f"⚠️ Клавиатура преподавателей: {e} (нормально, если нет подключения к БД)")
        
        # Проверяем, что в главном меню менеджера есть кнопка "Тесты"
        from manager.keyboards.main import get_manager_main_menu_kb
        main_kb = get_manager_main_menu_kb()
        
        has_tests_button = False
        for row in main_kb.inline_keyboard:
            for button in row:
                if "Тесты" in button.text and button.callback_data == "manager_tests":
                    has_tests_button = True
                    break
        
        if has_tests_button:
            print("✅ Кнопка 'Тесты' найдена в главном меню менеджера")
        else:
            print("❌ Кнопка 'Тесты' не найдена в главном меню менеджера")
            return False
        
        # Проверяем, что файл навигации существует
        import os
        if os.path.exists("common/register_handlers_and_transitions.py"):
            print("✅ Система навигации доступна")
        
        print("\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n📋 ПОТОК РАБОТЫ МЕНЕДЖЕРА:")
        print("   1. Главное меню → 🧠 Тесты")
        print("   2. Выбор типа теста (4 варианта)")
        print("   3. Выбор типа персонала (кураторы/преподаватели)")
        print("   4. Выбор конкретного сотрудника")
        print("   5. Дальше как у куратора/учителя")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Основная функция"""
    success = await test_manager_flow()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
