#!/usr/bin/env python3
"""
Отладка обработчиков менеджера
"""

import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def debug_manager_handlers():
    """Отладка обработчиков менеджера"""
    try:
        print("🔍 Отладка обработчиков менеджера...")
        
        # Импортируем роутер менеджера
        from manager.handlers.tests import router as manager_tests_router
        print("✅ Роутер тестов менеджера импортирован")
        
        # Проверяем количество обработчиков
        handlers_count = len(manager_tests_router.observers)
        print(f"📊 Количество обработчиков в роутере тестов менеджера: {handlers_count}")
        
        # Проверяем состояния
        from manager.states.states_tests import ManagerTestsStatisticsStates
        print(f"✅ Состояние month_control_select_group: {ManagerTestsStatisticsStates.month_control_select_group}")
        
        # Проверяем, что обработчики зарегистрированы правильно
        print("\n🔍 Анализ обработчиков:")
        for i, observer in enumerate(manager_tests_router.observers):
            print(f"  {i+1}. {observer}")
            
        # Проверяем основной роутер менеджера
        from manager.handlers import router as main_manager_router
        main_handlers_count = len(main_manager_router.observers)
        print(f"\n📊 Количество обработчиков в основном роутере менеджера: {main_handlers_count}")
        
        # Проверяем, включен ли роутер тестов в основной роутер
        included_routers = len(main_manager_router.sub_routers)
        print(f"📊 Количество подроутеров в основном роутере менеджера: {included_routers}")
        
        for i, sub_router in enumerate(main_manager_router.sub_routers):
            print(f"  Подроутер {i+1}: {sub_router}")
            if sub_router == manager_tests_router:
                print(f"    ✅ Роутер тестов найден в подроутерах!")
        
        # Проверяем callback_data паттерны
        print("\n🔍 Проверка callback_data паттернов:")
        test_callback_data = [
            "month_control_group_1",
            "month_control_group_2", 
            "month_entry_group_1",
            "ent_group_1",
            "course_entry_subject_1"
        ]
        
        for callback_data in test_callback_data:
            print(f"  📝 {callback_data}")
            
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при отладке: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Основная функция"""
    success = await debug_manager_handlers()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
